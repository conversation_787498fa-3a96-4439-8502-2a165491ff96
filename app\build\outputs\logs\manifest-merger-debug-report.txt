-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:2:1-124:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:2:1-124:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:2:1-124:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:2:1-124:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01e88d9fb2cb2aac7fc601b649cb4ad0\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ebc9b11a7c6b97531b9da952cf790a1\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aff9613c64aebdaea501daed69511013\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18c88091593c765440c3e46433f7b782\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4fcbdb669986d321a986cd0362dc66d\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad1688ecfc95c41d9251fb4427e87088\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fa1a8f1687dc566cc2a18fddb2629ba\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\190b79e9dcd497e2338cce628bbabc48\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af2cb1e44ab27d98b1f8e010c3a83f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159ec01479f3c9d22519fdc9d52a506f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a981be992523797564aa8f3e4908413\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b1f3ab03043bb5825a21f6e5b23b8f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9b72569aed363f4e01195f6f8922ccf\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a94340efa63585c81521e6eea393fd8c\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec0a180e484f5bbec645a07f9847fb70\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\250eed9b776ceeca081b9897139b3ff9\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\421dfc5a958708ffa97124b5ef7ec4b4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e279ba051dc7330ab17f6c0d107a8bd6\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87b8ae8a1db03b609da214a553c0d6b7\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0cdad1e8ece1b4152978173dc3e27c83\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4d8e9b57522d1f0afbb21fc11502490\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc776830e184c7f82bc43bc35afd2dd8\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81462dc117c952c2d71dbed443b79dda\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\151f5359afa908820169c578c2653d60\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1f7cae8cc12cae490390d03220b6681\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e21eb9b25fc1e41c8a8ce7105bf9dc1\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08dc03ae9b5dd0c4397385a685d9b269\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a12bbb69bed06160db19e0603508ad92\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ae937c67743d2517281677c688196a7\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c8034f566880048653f98ff2f0a8bfd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d95bcbc2744fc90a8c915185e861092a\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f102f0387ef6f11588061d3addc87bf3\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce70f358ccd137903b43f35c639616ee\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a7edb822d45499fc542fa3af7350599\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41e9a5398b69bf93287a5646b8037eec\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e930d80e49a845bb6ea173c8ca9682b2\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0634142971667196cfe5ed5093d84246\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98d177503c65cc86c557b08adedbe9ab\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72e6355490b0684dcdd9a6e35a68ff33\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0177ece6b73742f7c2fc71774399ab54\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a16170fefbd070a9e60ffa8d3b6a644f\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:6:5-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:6:22-75
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:7:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:7:22-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:12:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:14:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:14:22-73
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:16:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:16:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:17:5-87
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:17:22-84
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:18:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:18:22-65
supports-screens
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:21:5-25:40
	android:largeScreens
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:22:9-36
	android:smallScreens
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:24:9-36
	android:normalScreens
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:23:9-37
	android:xlargeScreens
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:25:9-37
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:27:5-123:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:27:5-123:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01e88d9fb2cb2aac7fc601b649cb4ad0\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01e88d9fb2cb2aac7fc601b649cb4ad0\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ebc9b11a7c6b97531b9da952cf790a1\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ebc9b11a7c6b97531b9da952cf790a1\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c8034f566880048653f98ff2f0a8bfd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c8034f566880048653f98ff2f0a8bfd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41e9a5398b69bf93287a5646b8037eec\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41e9a5398b69bf93287a5646b8037eec\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:36:9-52
	android:preserveLegacyExternalStorage
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:37:9-53
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:32:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:30:9-35
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:31:9-61
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:38:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:29:9-56
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:28:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:35:9-48
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:34:9-69
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:33:9-44
activity#com.example.floatingai.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:40:9-53:20
	android:launchMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:43:13-43
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:42:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:41:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:44:13-47:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:45:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:45:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:46:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:46:27-74
intent-filter#action:name:android.intent.action.SEND+category:name:android.intent.category.DEFAULT+data:mimeType:image/*
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:48:13-52:29
action#android.intent.action.SEND
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:49:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:49:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:50:17-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:50:27-73
data
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:51:17-52
	android:mimeType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:51:23-49
activity#com.example.floatingai.SettingsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:55:9-63:20
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:58:13-55
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:59:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:57:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:56:13-45
intent-filter#action:name:android.intent.action.VIEW
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:60:13-62:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:61:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:61:25-66
activity#com.example.floatingai.AreaSelectionActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:66:9-69:80
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:68:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:69:13-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:67:13-50
activity#com.example.floatingai.AccessibilitySetupActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:72:9-75:74
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:74:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:75:13-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:73:13-55
activity#com.example.floatingai.ChatHistoryActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:78:9-85:20
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:81:13-55
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:80:13-47
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:79:13-48
meta-data#android.support.PARENT_ACTIVITY
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:82:13-84:53
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:84:17-50
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:83:17-63
activity#com.example.floatingai.ConversationViewActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:88:9-95:20
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:91:13-62
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:90:13-45
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:89:13-53
activity#com.example.floatingai.OtherOptionsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:98:9-102:67
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:101:13-55
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:100:13-43
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:102:13-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:99:13-49
service#com.example.floatingai.FloatingService
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:105:9-109:19
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:107:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:108:13-53
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:106:13-44
service#com.example.floatingai.ScreenCaptureAccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:112:9-122:19
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:115:13-36
	android:permission
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:114:13-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:113:13-62
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:116:13-118:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:117:17-92
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:117:25-89
meta-data#android.accessibilityservice
ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:119:13-121:72
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:121:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:120:17-60
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01e88d9fb2cb2aac7fc601b649cb4ad0\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01e88d9fb2cb2aac7fc601b649cb4ad0\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ebc9b11a7c6b97531b9da952cf790a1\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ebc9b11a7c6b97531b9da952cf790a1\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aff9613c64aebdaea501daed69511013\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aff9613c64aebdaea501daed69511013\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18c88091593c765440c3e46433f7b782\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18c88091593c765440c3e46433f7b782\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4fcbdb669986d321a986cd0362dc66d\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4fcbdb669986d321a986cd0362dc66d\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad1688ecfc95c41d9251fb4427e87088\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad1688ecfc95c41d9251fb4427e87088\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fa1a8f1687dc566cc2a18fddb2629ba\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fa1a8f1687dc566cc2a18fddb2629ba\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\190b79e9dcd497e2338cce628bbabc48\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\190b79e9dcd497e2338cce628bbabc48\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af2cb1e44ab27d98b1f8e010c3a83f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af2cb1e44ab27d98b1f8e010c3a83f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159ec01479f3c9d22519fdc9d52a506f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159ec01479f3c9d22519fdc9d52a506f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a981be992523797564aa8f3e4908413\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a981be992523797564aa8f3e4908413\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b1f3ab03043bb5825a21f6e5b23b8f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b1f3ab03043bb5825a21f6e5b23b8f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9b72569aed363f4e01195f6f8922ccf\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9b72569aed363f4e01195f6f8922ccf\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a94340efa63585c81521e6eea393fd8c\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a94340efa63585c81521e6eea393fd8c\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec0a180e484f5bbec645a07f9847fb70\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec0a180e484f5bbec645a07f9847fb70\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\250eed9b776ceeca081b9897139b3ff9\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\250eed9b776ceeca081b9897139b3ff9\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\421dfc5a958708ffa97124b5ef7ec4b4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\421dfc5a958708ffa97124b5ef7ec4b4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e279ba051dc7330ab17f6c0d107a8bd6\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e279ba051dc7330ab17f6c0d107a8bd6\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87b8ae8a1db03b609da214a553c0d6b7\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87b8ae8a1db03b609da214a553c0d6b7\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0cdad1e8ece1b4152978173dc3e27c83\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0cdad1e8ece1b4152978173dc3e27c83\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4d8e9b57522d1f0afbb21fc11502490\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4d8e9b57522d1f0afbb21fc11502490\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc776830e184c7f82bc43bc35afd2dd8\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc776830e184c7f82bc43bc35afd2dd8\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81462dc117c952c2d71dbed443b79dda\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81462dc117c952c2d71dbed443b79dda\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\151f5359afa908820169c578c2653d60\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\151f5359afa908820169c578c2653d60\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1f7cae8cc12cae490390d03220b6681\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1f7cae8cc12cae490390d03220b6681\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e21eb9b25fc1e41c8a8ce7105bf9dc1\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e21eb9b25fc1e41c8a8ce7105bf9dc1\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08dc03ae9b5dd0c4397385a685d9b269\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08dc03ae9b5dd0c4397385a685d9b269\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a12bbb69bed06160db19e0603508ad92\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a12bbb69bed06160db19e0603508ad92\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ae937c67743d2517281677c688196a7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ae937c67743d2517281677c688196a7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c8034f566880048653f98ff2f0a8bfd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c8034f566880048653f98ff2f0a8bfd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d95bcbc2744fc90a8c915185e861092a\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d95bcbc2744fc90a8c915185e861092a\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f102f0387ef6f11588061d3addc87bf3\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f102f0387ef6f11588061d3addc87bf3\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce70f358ccd137903b43f35c639616ee\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce70f358ccd137903b43f35c639616ee\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a7edb822d45499fc542fa3af7350599\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a7edb822d45499fc542fa3af7350599\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41e9a5398b69bf93287a5646b8037eec\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41e9a5398b69bf93287a5646b8037eec\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e930d80e49a845bb6ea173c8ca9682b2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e930d80e49a845bb6ea173c8ca9682b2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0634142971667196cfe5ed5093d84246\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0634142971667196cfe5ed5093d84246\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98d177503c65cc86c557b08adedbe9ab\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98d177503c65cc86c557b08adedbe9ab\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72e6355490b0684dcdd9a6e35a68ff33\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72e6355490b0684dcdd9a6e35a68ff33\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0177ece6b73742f7c2fc71774399ab54\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0177ece6b73742f7c2fc71774399ab54\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a16170fefbd070a9e60ffa8d3b6a644f\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a16170fefbd070a9e60ffa8d3b6a644f\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c8034f566880048653f98ff2f0a8bfd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c8034f566880048653f98ff2f0a8bfd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.example.floatingai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.floatingai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
