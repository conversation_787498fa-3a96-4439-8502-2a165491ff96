package com.example.floatingai;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.os.Bundle;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Toast;

import java.io.File;
import java.io.FileOutputStream;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

public class AreaSelectionActivity extends Activity {
    
    private static final String TAG = "AreaSelectionActivity";
    
    private ImageView imageView;
    private SelectionOverlay selectionOverlay;
    private String fullScreenPath;
    private String userMessage;
    private Bitmap fullScreenBitmap;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Obtener datos del intent
        fullScreenPath = getIntent().getStringExtra("full_screen_path");
        userMessage = getIntent().getStringExtra("user_message");
        
        if (fullScreenPath == null) {
            Toast.makeText(this, "Error: No se encontró la captura", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        // Cargar la imagen capturada
        fullScreenBitmap = BitmapFactory.decodeFile(fullScreenPath);
        if (fullScreenBitmap == null) {
            Toast.makeText(this, "Error: No se pudo cargar la captura", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        setupUI();
    }
    
    private void setupUI() {
        // Crear layout principal
        RelativeLayout mainLayout = new RelativeLayout(this);
        mainLayout.setBackgroundColor(Color.BLACK);
        
        // ImageView para mostrar la captura
        imageView = new ImageView(this);
        imageView.setImageBitmap(fullScreenBitmap);
        imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        imageView.setId(View.generateViewId());
        
        RelativeLayout.LayoutParams imageParams = new RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        );
        mainLayout.addView(imageView, imageParams);
        
        // Overlay de selección
        selectionOverlay = new SelectionOverlay(this);
        RelativeLayout.LayoutParams overlayParams = new RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        );
        mainLayout.addView(selectionOverlay, overlayParams);
        
        // Botones de control
        addControlButtons(mainLayout);
        
        setContentView(mainLayout);
    }
    
    private void addControlButtons(RelativeLayout mainLayout) {
        // Common size and styles for square buttons
        int buttonSize = 72; // Size in dp for square buttons (reduced by 20% from 90dp)
        int buttonSizePx = (int) (buttonSize * getResources().getDisplayMetrics().density);
        
        // Get screen width to calculate positions
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        
        // Crear posiciones relativas para los botones (en porcentajes del ancho de pantalla)
        // Posición del botón verde: 20% desde la izquierda
        // Posición del botón azul: 50% (centro)
        // Posición del botón rojo: 80% desde la izquierda (20% desde la derecha)
        int leftPos = screenWidth / 5;      // 20% del ancho
        int rightPos = screenWidth * 4 / 5; // 80% del ancho
        
        // Botón Capturar (Green check)
        Button captureButton = new Button(this);
        captureButton.setText("✓");
        captureButton.setBackgroundColor(Color.GREEN);
        captureButton.setTextColor(Color.WHITE);
        captureButton.setTextSize(24);
        captureButton.setOnClickListener(v -> captureSelectedArea());
        
        RelativeLayout.LayoutParams captureParams = new RelativeLayout.LayoutParams(
            buttonSizePx,
            buttonSizePx
        );
        captureParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        // Centrar el botón en su posición horizontal
        captureParams.leftMargin = leftPos - buttonSizePx / 2;
        captureParams.bottomMargin = 200; // 200dp from bottom
        
        // Botón SS (Captura completa - Blue)
        Button fullScreenButton = new Button(this);
        fullScreenButton.setText("SS");
        fullScreenButton.setBackgroundColor(Color.BLUE);
        fullScreenButton.setTextColor(Color.WHITE);
        fullScreenButton.setTextSize(24);
        fullScreenButton.setOnClickListener(v -> captureFullScreen());
        
        RelativeLayout.LayoutParams fullScreenParams = new RelativeLayout.LayoutParams(
            buttonSizePx,
            buttonSizePx
        );
        fullScreenParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        fullScreenParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
        fullScreenParams.bottomMargin = 200; // 200dp from bottom
        
        // Botón Cancelar (Red X)
        Button cancelButton = new Button(this);
        cancelButton.setText("✕");
        cancelButton.setBackgroundColor(Color.RED);
        cancelButton.setTextColor(Color.WHITE);
        cancelButton.setTextSize(24);
        cancelButton.setOnClickListener(v -> {
            // Notificar al FloatingService que la captura fue cancelada
            Intent broadcastIntent = new Intent(FloatingService.ACTION_RESTORE_CHAT_WINDOW);
            LocalBroadcastManager.getInstance(this).sendBroadcast(broadcastIntent);
            Log.d(TAG, "Sent LOCAL broadcast for capture cancellation.");

            // Intentar minimizar la tarea actual (volver al Home)
            moveTaskToBack(true);

            cleanupAndFinish();
        });
        
        RelativeLayout.LayoutParams cancelParams = new RelativeLayout.LayoutParams(
            buttonSizePx,
            buttonSizePx
        );
        cancelParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        // Centrar el botón en su posición horizontal
        cancelParams.leftMargin = rightPos - buttonSizePx / 2;
        cancelParams.bottomMargin = 200; // 200dp from bottom
        
        mainLayout.addView(captureButton, captureParams);
        mainLayout.addView(fullScreenButton, fullScreenParams);
        mainLayout.addView(cancelButton, cancelParams);
    }

    private Rect convertViewCoordinatesToBitmapCoordinates(Rect viewRect) {
        // Obtener dimensiones de la vista del ImageView
        int viewWidth = imageView.getWidth();
        int viewHeight = imageView.getHeight();

        // Obtener dimensiones del bitmap original
        int bitmapWidth = fullScreenBitmap.getWidth();
        int bitmapHeight = fullScreenBitmap.getHeight();

        // Calcular la escala para CENTER_CROP
        float scaleX = (float) viewWidth / bitmapWidth;
        float scaleY = (float) viewHeight / bitmapHeight;
        float scale = Math.max(scaleX, scaleY); // CENTER_CROP usa la escala mayor

        // Calcular las dimensiones escaladas del bitmap
        float scaledBitmapWidth = bitmapWidth * scale;
        float scaledBitmapHeight = bitmapHeight * scale;

        // Calcular el offset para centrar la imagen escalada
        float offsetX = (viewWidth - scaledBitmapWidth) / 2f;
        float offsetY = (viewHeight - scaledBitmapHeight) / 2f;

        // Convertir coordenadas de la vista a coordenadas del bitmap
        float bitmapLeft = (viewRect.left - offsetX) / scale;
        float bitmapTop = (viewRect.top - offsetY) / scale;
        float bitmapRight = (viewRect.right - offsetX) / scale;
        float bitmapBottom = (viewRect.bottom - offsetY) / scale;

        // Asegurar que las coordenadas estén dentro de los límites del bitmap
        bitmapLeft = Math.max(0, Math.min(bitmapLeft, bitmapWidth));
        bitmapTop = Math.max(0, Math.min(bitmapTop, bitmapHeight));
        bitmapRight = Math.max(bitmapLeft, Math.min(bitmapRight, bitmapWidth));
        bitmapBottom = Math.max(bitmapTop, Math.min(bitmapBottom, bitmapHeight));

        return new Rect(
            Math.round(bitmapLeft),
            Math.round(bitmapTop),
            Math.round(bitmapRight),
            Math.round(bitmapBottom)
        );
    }

    private void captureSelectedArea() {
        Rect selectedArea = selectionOverlay.getSelectedArea();

        if (selectedArea.width() < 50 || selectedArea.height() < 50) {
            Toast.makeText(this, "Selecciona un área más grande", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Capturing area: " + selectedArea.toString());

        try {
            // Convertir coordenadas de la vista a coordenadas del bitmap original
            Rect bitmapArea = convertViewCoordinatesToBitmapCoordinates(selectedArea);

            Log.d(TAG, "Bitmap area after conversion: " + bitmapArea.toString());

            // Recortar el área seleccionada
            Bitmap croppedBitmap = Bitmap.createBitmap(
                fullScreenBitmap,
                bitmapArea.left,
                bitmapArea.top,
                bitmapArea.width(),
                bitmapArea.height()
            );

            // Guardar el área recortada
            String croppedPath = saveCroppedBitmap(croppedBitmap);

            if (croppedPath != null) {
                // Enviar al FloatingService
                sendToFloatingService(croppedPath);
                croppedBitmap.recycle();
                cleanupAndFinish();
            } else {
                Toast.makeText(this, "Error al guardar el área seleccionada", Toast.LENGTH_SHORT).show();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error cropping area", e);
            Toast.makeText(this, "Error al recortar área: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    private void captureFullScreen() {
        Log.d(TAG, "Capturing full screen");
        
        try {
            // Crear una copia del bitmap de pantalla completa
            Bitmap fullScreenCopy = fullScreenBitmap.copy(fullScreenBitmap.getConfig(), false);
            
            // Guardar la captura completa
            String fullScreenPath = saveFullScreenBitmap(fullScreenCopy);
            
            if (fullScreenPath != null) {
                // Enviar al FloatingService
                sendToFloatingService(fullScreenPath);
                fullScreenCopy.recycle();
                cleanupAndFinish();
            } else {
                Toast.makeText(this, "Error al guardar la captura completa", Toast.LENGTH_SHORT).show();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error capturing full screen", e);
            Toast.makeText(this, "Error al capturar pantalla completa: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    private String saveCroppedBitmap(Bitmap bitmap) {
        try {
            String filename = "cropped_" + System.currentTimeMillis() + ".jpg";
            FileOutputStream fos = openFileOutput(filename, MODE_PRIVATE);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, fos);
            fos.close();
            
            File file = new File(getFilesDir(), filename);
            return file.getAbsolutePath();
        } catch (Exception e) {
            Log.e(TAG, "Error saving cropped bitmap", e);
            return null;
        }
    }
    
    private String saveFullScreenBitmap(Bitmap bitmap) {
        try {
            String filename = "fullscreen_" + System.currentTimeMillis() + ".jpg";
            FileOutputStream fos = openFileOutput(filename, MODE_PRIVATE);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, fos);
            fos.close();
            
            File file = new File(getFilesDir(), filename);
            return file.getAbsolutePath();
        } catch (Exception e) {
            Log.e(TAG, "Error saving full screen bitmap", e);
            return null;
        }
    }
    
    private void sendToFloatingService(String screenshotPath) {
        Intent intent = new Intent(this, FloatingService.class);
        intent.setAction(FloatingService.ACTION_IMAGE_CAPTURED_FOR_CHAT);
        intent.putExtra("screenshot_path", screenshotPath);
        intent.putExtra("save_to_gallery", true);
        startService(intent);
        
        moveTaskToBack(true);
    }
    
    private void cleanupAndFinish() {
        // Limpiar archivo de captura completa
        try {
            File fullScreenFile = new File(fullScreenPath);
            if (fullScreenFile.exists()) {
                fullScreenFile.delete();
                Log.d(TAG, "Full screen file deleted");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error deleting full screen file", e);
        }
        
        if (fullScreenBitmap != null && !fullScreenBitmap.isRecycled()) {
            fullScreenBitmap.recycle();
        }
        
        finish();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        cleanupAndFinish();
    }
    
    // Clase para el overlay de selección
    private class SelectionOverlay extends View {
        private Paint borderPaint;
        private Paint fillPaint;
        private Paint dimPaint;
        private RectF selectedArea;
        private boolean isDragging = false;
        private int dragHandle = -1; // 0=top-left, 1=top-right, 2=bottom-left, 3=bottom-right, 4=move
        private float lastTouchX, lastTouchY;
        private int handleSize = 60;
        
        public SelectionOverlay(Activity context) {
            super(context);
            init();
        }
        
        private void init() {
            borderPaint = new Paint();
            borderPaint.setColor(Color.CYAN);
            borderPaint.setStyle(Paint.Style.STROKE);
            borderPaint.setStrokeWidth(4);
            
            fillPaint = new Paint();
            fillPaint.setColor(Color.argb(30, 0, 255, 255));
            fillPaint.setStyle(Paint.Style.FILL);
            
            dimPaint = new Paint();
            dimPaint.setColor(Color.argb(100, 0, 0, 0));
            dimPaint.setStyle(Paint.Style.FILL);
            
            // Área inicial (centro de la pantalla, 1/3 del tamaño)
            int screenWidth = getResources().getDisplayMetrics().widthPixels;
            int screenHeight = getResources().getDisplayMetrics().heightPixels;
            
            int width = screenWidth / 2;
            int height = screenHeight / 2;
            int left = (screenWidth - width) / 2;
            int top = (screenHeight - height) / 2;
            
            selectedArea = new RectF(left, top, left + width, top + height);
        }
        
        @Override
        protected void onDraw(Canvas canvas) {
            super.onDraw(canvas);
            
            // Dibujar área oscurecida alrededor de la selección
            canvas.drawRect(0, 0, getWidth(), selectedArea.top, dimPaint);
            canvas.drawRect(0, selectedArea.top, selectedArea.left, selectedArea.bottom, dimPaint);
            canvas.drawRect(selectedArea.right, selectedArea.top, getWidth(), selectedArea.bottom, dimPaint);
            canvas.drawRect(0, selectedArea.bottom, getWidth(), getHeight(), dimPaint);
            
            // Dibujar área seleccionada
            canvas.drawRect(selectedArea, fillPaint);
            canvas.drawRect(selectedArea, borderPaint);
            
            // Dibujar handles de redimensionamiento
            drawHandle(canvas, selectedArea.left, selectedArea.top); // top-left
            drawHandle(canvas, selectedArea.right, selectedArea.top); // top-right
            drawHandle(canvas, selectedArea.left, selectedArea.bottom); // bottom-left
            drawHandle(canvas, selectedArea.right, selectedArea.bottom); // bottom-right
        }
        
        private void drawHandle(Canvas canvas, float x, float y) {
            Paint handlePaint = new Paint();
            handlePaint.setColor(Color.CYAN);
            handlePaint.setStyle(Paint.Style.FILL);
            
            canvas.drawCircle(x, y, 15, handlePaint);
            
            handlePaint.setColor(Color.WHITE);
            canvas.drawCircle(x, y, 10, handlePaint);
        }
        
        @Override
        public boolean onTouchEvent(MotionEvent event) {
            float x = event.getX();
            float y = event.getY();
            
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    dragHandle = getHandleAt(x, y);
                    if (dragHandle != -1) {
                        isDragging = true;
                        lastTouchX = x;
                        lastTouchY = y;
                        return true;
                    }
                    break;
                    
                case MotionEvent.ACTION_MOVE:
                    if (isDragging && dragHandle != -1) {
                        updateSelectedArea(x, y);
                        invalidate();
                        return true;
                    }
                    break;
                    
                case MotionEvent.ACTION_UP:
                    isDragging = false;
                    dragHandle = -1;
                    break;
            }
            
            return super.onTouchEvent(event);
        }
        
        private int getHandleAt(float x, float y) {
            // Verificar handles de esquinas
            if (Math.abs(x - selectedArea.left) < handleSize && Math.abs(y - selectedArea.top) < handleSize) {
                return 0; // top-left
            }
            if (Math.abs(x - selectedArea.right) < handleSize && Math.abs(y - selectedArea.top) < handleSize) {
                return 1; // top-right
            }
            if (Math.abs(x - selectedArea.left) < handleSize && Math.abs(y - selectedArea.bottom) < handleSize) {
                return 2; // bottom-left
            }
            if (Math.abs(x - selectedArea.right) < handleSize && Math.abs(y - selectedArea.bottom) < handleSize) {
                return 3; // bottom-right
            }
            
            // Verificar si está dentro del área para mover
            if (selectedArea.contains(x, y)) {
                return 4; // move
            }
            
            return -1;
        }
        
        private void updateSelectedArea(float x, float y) {
            float deltaX = x - lastTouchX;
            float deltaY = y - lastTouchY;
            
            switch (dragHandle) {
                case 0: // top-left
                    selectedArea.left = Math.max(0, Math.min(selectedArea.left + deltaX, selectedArea.right - 100));
                    selectedArea.top = Math.max(0, Math.min(selectedArea.top + deltaY, selectedArea.bottom - 100));
                    break;
                case 1: // top-right
                    selectedArea.right = Math.min(getWidth(), Math.max(selectedArea.right + deltaX, selectedArea.left + 100));
                    selectedArea.top = Math.max(0, Math.min(selectedArea.top + deltaY, selectedArea.bottom - 100));
                    break;
                case 2: // bottom-left
                    selectedArea.left = Math.max(0, Math.min(selectedArea.left + deltaX, selectedArea.right - 100));
                    selectedArea.bottom = Math.min(getHeight(), Math.max(selectedArea.bottom + deltaY, selectedArea.top + 100));
                    break;
                case 3: // bottom-right
                    selectedArea.right = Math.min(getWidth(), Math.max(selectedArea.right + deltaX, selectedArea.left + 100));
                    selectedArea.bottom = Math.min(getHeight(), Math.max(selectedArea.bottom + deltaY, selectedArea.top + 100));
                    break;
                case 4: // move
                    float newLeft = selectedArea.left + deltaX;
                    float newTop = selectedArea.top + deltaY;
                    float newRight = selectedArea.right + deltaX;
                    float newBottom = selectedArea.bottom + deltaY;
                    
                    if (newLeft >= 0 && newRight <= getWidth()) {
                        selectedArea.left = newLeft;
                        selectedArea.right = newRight;
                    }
                    if (newTop >= 0 && newBottom <= getHeight()) {
                        selectedArea.top = newTop;
                        selectedArea.bottom = newBottom;
                    }
                    break;
            }
            
            lastTouchX = x;
            lastTouchY = y;
        }
        
        public Rect getSelectedArea() {
            return new Rect(
                (int) selectedArea.left,
                (int) selectedArea.top,
                (int) selectedArea.right,
                (int) selectedArea.bottom
            );
        }
    }
} 