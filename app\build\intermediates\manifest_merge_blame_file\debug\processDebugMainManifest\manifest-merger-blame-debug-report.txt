1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.floatingai"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permisos necesarios -->
12    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
12-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:6:5-78
12-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:6:22-75
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:7:5-67
13-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:7:22-64
14
15    <!-- Permisos de almacenamiento con compatibilidad para diferentes versiones -->
16    <uses-permission
16-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:10:22-78
18        android:maxSdkVersion="28" />
18-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission
19-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:12:5-13:38
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:12:22-77
21        android:maxSdkVersion="32" />
21-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:13:9-35
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:14:5-76
22-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:14:22-73
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:16:5-77
23-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:16:22-74
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
24-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:17:5-87
24-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:17:22-84
25    <uses-permission android:name="android.permission.WAKE_LOCK" />
25-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:18:5-68
25-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:18:22-65
26
27    <!-- Declarar compatibilidad con diferentes arquitecturas -->
28    <supports-screens
28-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:21:5-25:40
29        android:largeScreens="true"
29-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:22:9-36
30        android:normalScreens="true"
30-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:23:9-37
31        android:smallScreens="true"
31-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:24:9-36
32        android:xlargeScreens="true" />
32-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:25:9-37
33
34    <permission
34-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
35        android:name="com.example.floatingai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.example.floatingai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
39
40    <application
40-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:27:5-123:19
41        android:allowBackup="true"
41-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:28:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53e0f3f7ab683014a63a61d9b9d90c07\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:icon="@android:drawable/ic_dialog_info"
45-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:29:9-56
46        android:label="FloatingAI"
46-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:30:9-35
47        android:networkSecurityConfig="@xml/network_security_config"
47-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:34:9-69
48        android:preserveLegacyExternalStorage="true"
48-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:37:9-53
49        android:requestLegacyExternalStorage="true"
49-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:36:9-52
50        android:roundIcon="@android:drawable/ic_dialog_info"
50-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:31:9-61
51        android:supportsRtl="true"
51-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:32:9-35
52        android:testOnly="true"
53        android:theme="@style/Theme.FloatingAI"
53-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:35:9-48
54        android:usesCleartextTraffic="true" >
54-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:33:9-44
55        <activity
55-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:40:9-53:20
56            android:name="com.example.floatingai.MainActivity"
56-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:41:13-41
57            android:exported="true"
57-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:42:13-36
58            android:launchMode="singleTop" >
58-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:43:13-43
59            <intent-filter>
59-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:44:13-47:29
60                <action android:name="android.intent.action.MAIN" />
60-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:45:17-69
60-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:45:25-66
61
62                <category android:name="android.intent.category.LAUNCHER" />
62-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:46:17-77
62-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:46:27-74
63            </intent-filter>
64            <intent-filter>
64-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:48:13-52:29
65                <action android:name="android.intent.action.SEND" />
65-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:49:17-69
65-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:49:25-66
66
67                <category android:name="android.intent.category.DEFAULT" />
67-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:50:17-76
67-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:50:27-73
68
69                <data android:mimeType="image/*" />
69-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:51:17-52
69-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:51:23-49
70            </intent-filter>
71        </activity>
72        <activity
72-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:55:9-63:20
73            android:name="com.example.floatingai.SettingsActivity"
73-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:56:13-45
74            android:exported="false"
74-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:57:13-37
75            android:label="Configuración IA"
75-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:59:13-45
76            android:parentActivityName="com.example.floatingai.MainActivity" >
76-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:58:13-55
77            <intent-filter>
77-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:60:13-62:29
78                <action android:name="android.intent.action.VIEW" />
78-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:61:17-69
78-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:61:25-66
79            </intent-filter>
80        </activity>
81
82        <!-- Activity para selección de área -->
83        <activity
83-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:66:9-69:80
84            android:name="com.example.floatingai.AreaSelectionActivity"
84-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:67:13-50
85            android:exported="false"
85-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:68:13-37
86            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />
86-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:69:13-77
87
88        <!-- Activity para configurar accesibilidad -->
89        <activity
89-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:72:9-75:74
90            android:name="com.example.floatingai.AccessibilitySetupActivity"
90-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:73:13-55
91            android:exported="false"
91-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:74:13-37
92            android:theme="@android:style/Theme.Material.Light.Dialog" />
92-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:75:13-71
93
94        <!-- Activity para ver el historial de chat -->
95        <activity
95-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:78:9-85:20
96            android:name="com.example.floatingai.ChatHistoryActivity"
96-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:79:13-48
97            android:label="Historial de Chats"
97-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:80:13-47
98            android:parentActivityName="com.example.floatingai.MainActivity" >
98-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:81:13-55
99            <meta-data
99-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:82:13-84:53
100                android:name="android.support.PARENT_ACTIVITY"
100-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:83:17-63
101                android:value=".SettingsActivity" />
101-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:84:17-50
102        </activity>
103
104        <!-- Activity para ver los mensajes de una conversación específica -->
105        <activity
105-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:88:9-95:20
106            android:name="com.example.floatingai.ConversationViewActivity"
106-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:89:13-53
107            android:label="Ver Conversación"
107-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:90:13-45
108            android:parentActivityName="com.example.floatingai.ChatHistoryActivity" >
108-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:91:13-62
109            <meta-data
109-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:82:13-84:53
110                android:name="android.support.PARENT_ACTIVITY"
110-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:83:17-63
111                android:value=".ChatHistoryActivity" />
111-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:84:17-50
112        </activity>
113
114        <!-- Activity para otras opciones -->
115        <activity
115-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:98:9-102:67
116            android:name="com.example.floatingai.OtherOptionsActivity"
116-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:99:13-49
117            android:label="Otras Opciones"
117-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:100:13-43
118            android:parentActivityName="com.example.floatingai.MainActivity"
118-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:101:13-55
119            android:theme="@style/Theme.FloatingAI.NoActionBar" />
119-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:102:13-64
120
121        <!-- Service para la burbuja flotante -->
122        <service
122-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:105:9-109:19
123            android:name="com.example.floatingai.FloatingService"
123-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:106:13-44
124            android:exported="false"
124-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:107:13-37
125            android:foregroundServiceType="dataSync" >
125-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:108:13-53
126        </service>
127
128        <!-- Servicio de Accesibilidad para captura de pantalla -->
129        <service
129-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:112:9-122:19
130            android:name="com.example.floatingai.ScreenCaptureAccessibilityService"
130-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:113:13-62
131            android:exported="true"
131-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:115:13-36
132            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
132-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:114:13-79
133            <intent-filter>
133-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:116:13-118:29
134                <action android:name="android.accessibilityservice.AccessibilityService" />
134-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:117:17-92
134-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:117:25-89
135            </intent-filter>
136
137            <meta-data
137-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:119:13-121:72
138                android:name="android.accessibilityservice"
138-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:120:17-60
139                android:resource="@xml/accessibility_service_config" />
139-->C:\Users\<USER>\AndroidStudioProjects\FloatingAI\app\src\main\AndroidManifest.xml:121:17-69
140        </service>
141
142        <provider
142-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
143            android:name="androidx.startup.InitializationProvider"
143-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
144            android:authorities="com.example.floatingai.androidx-startup"
144-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
145            android:exported="false" >
145-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
146            <meta-data
146-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
147                android:name="androidx.emoji2.text.EmojiCompatInitializer"
147-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
148                android:value="androidx.startup" />
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb8cf479e26c3d773d7a79dff745e29b\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
149            <meta-data
149-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
150                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
150-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
151                android:value="androidx.startup" />
151-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64391a074df49c6bd225f8e022185bfa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
152            <meta-data
152-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
153                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
153-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
154                android:value="androidx.startup" />
154-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
155        </provider>
156
157        <receiver
157-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
158            android:name="androidx.profileinstaller.ProfileInstallReceiver"
158-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
159            android:directBootAware="false"
159-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
160            android:enabled="true"
160-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
161            android:exported="true"
161-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
162            android:permission="android.permission.DUMP" >
162-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
163            <intent-filter>
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
164                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
165            </intent-filter>
166            <intent-filter>
166-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
167                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
167-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
167-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
168            </intent-filter>
169            <intent-filter>
169-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
170                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
170-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
170-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
171            </intent-filter>
172            <intent-filter>
172-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
173                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
173-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
173-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85ffa77242f081cb1fca2d119b14ec34\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
174            </intent-filter>
175        </receiver>
176    </application>
177
178</manifest>
