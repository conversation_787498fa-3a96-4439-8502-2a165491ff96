<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.floatingai"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <!-- Permisos necesarios -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Permisos de almacenamiento con compatibilidad para diferentes versiones -->
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Declarar compatibilidad con diferentes arquitecturas -->
    <supports-screens
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true"
        android:xlargeScreens="true" />

    <permission
        android:name="com.example.floatingai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.floatingai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@android:drawable/ic_dialog_info"
        android:label="FloatingAI"
        android:networkSecurityConfig="@xml/network_security_config"
        android:preserveLegacyExternalStorage="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@android:drawable/ic_dialog_info"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.FloatingAI"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.example.floatingai.MainActivity"
            android:exported="true"
            android:launchMode="singleTop" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="image/*" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.example.floatingai.SettingsActivity"
            android:exported="false"
            android:label="Configuración IA"
            android:parentActivityName="com.example.floatingai.MainActivity" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>

        <!-- Activity para selección de área -->
        <activity
            android:name="com.example.floatingai.AreaSelectionActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />

        <!-- Activity para configurar accesibilidad -->
        <activity
            android:name="com.example.floatingai.AccessibilitySetupActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Material.Light.Dialog" />

        <!-- Activity para ver el historial de chat -->
        <activity
            android:name="com.example.floatingai.ChatHistoryActivity"
            android:label="Historial de Chats"
            android:parentActivityName="com.example.floatingai.MainActivity" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".SettingsActivity" />
        </activity>

        <!-- Activity para ver los mensajes de una conversación específica -->
        <activity
            android:name="com.example.floatingai.ConversationViewActivity"
            android:label="Ver Conversación"
            android:parentActivityName="com.example.floatingai.ChatHistoryActivity" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".ChatHistoryActivity" />
        </activity>

        <!-- Activity para otras opciones -->
        <activity
            android:name="com.example.floatingai.OtherOptionsActivity"
            android:label="Otras Opciones"
            android:parentActivityName="com.example.floatingai.MainActivity"
            android:theme="@style/Theme.FloatingAI.NoActionBar" />

        <!-- Service para la burbuja flotante -->
        <service
            android:name="com.example.floatingai.FloatingService"
            android:exported="false"
            android:foregroundServiceType="dataSync" >
        </service>

        <!-- Servicio de Accesibilidad para captura de pantalla -->
        <service
            android:name="com.example.floatingai.ScreenCaptureAccessibilityService"
            android:exported="true"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.floatingai.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>